{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Product\\\\Addproduct\\\\AddProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { NavBar } from '../../../components/Navbar/Navbar';\nimport { Sidebar } from '../../../components/SidePanel/Sidebar.jsx';\nimport { MediaComponent, InventoryComponent, VariantTable, OrganizeAndClassifyComponent, WeightAndDimensionsComponent, VariantsComponent, TitleAndDescriptionComponent } from './AddProductComponent.jsx';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faSave, faArrowLeft, faUpload } from '@fortawesome/free-solid-svg-icons';\nimport './addProductPage.css';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport { Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, DialogTitle } from '@mui/material'; // Import Material-UI components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const AddProductPage = () => {\n  _s();\n  const {\n    productId\n  } = useParams();\n  const navigate = useNavigate();\n  const [dialogVisible, setDialogVisible] = useState(false); // Define dialog visibility state\n\n  // Define openDialog function\n  const openDialog = () => {\n    setDialogVisible(true);\n  };\n  const [product, setProduct] = useState({\n    newProduct: {\n      fields: {\n        title: '',\n        tags: '',\n        brand: '',\n        weight: {\n          unit: 'kg',\n          value: ''\n        },\n        dimensions: {\n          unit: 'm',\n          width: '',\n          height: '',\n          length: ''\n        },\n        short_description: '',\n        categories: ['']\n      },\n      photos: ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG', 'https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\n      variants: [],\n      options: {\n        option1: {\n          name: 'color',\n          values: ['red']\n        },\n        option2: {\n          name: 'color',\n          values: ['black']\n        }\n      },\n      isUpdate: 'no'\n    }\n  });\n  useEffect(() => {\n    if (productId) {\n      fetchProductDetails(productId);\n    }\n  }, [productId]);\n  const fetchProductDetails = async productId => {\n    try {\n      const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_product/?fby_user_id=8&sku=${productId}`, {\n        isCreated: true\n      }, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        }\n      });\n      const newProduct = response.data.newProduct;\n      setProduct({\n        newProduct: {\n          fields: {\n            title: newProduct.fields.title,\n            tags: newProduct.fields.tags,\n            brand: newProduct.fields.brand,\n            price: newProduct.fields.price,\n            quantity: newProduct.fields.inventory_quantity,\n            sku: newProduct.fields.sku,\n            barcode: newProduct.fields.barcode,\n            asin: newProduct.fields.asin,\n            weight: {\n              value: newProduct.fields.weight_value,\n              unit: newProduct.fields.weight_unit\n            },\n            dimensions: {\n              unit: newProduct.fields.dimensions_unit || 'm',\n              width: newProduct.fields.dimensions_width || '',\n              height: newProduct.fields.dimensions_height || '',\n              length: newProduct.fields.dimensions_length || ''\n            },\n            short_description: newProduct.fields.description,\n            categories: [newProduct.fields.category]\n          },\n          photos: newProduct.photos.map(photo => photo.image),\n          variants: newProduct.variants.map(variant => ({\n            id: variant.id,\n            sku: variant.sku,\n            barcode: variant.barcode,\n            item_id: variant.item_id,\n            title: variant.title,\n            inventory_quantity: variant.inventory_quantity,\n            image: variant.image,\n            price: variant.price,\n            specialPrice: variant.specialPrice,\n            skuFamily: variant.skuFamily\n          })),\n          options: [{\n            name: newProduct.options[0].name,\n            values: newProduct.options[0].values\n          }, {\n            name: newProduct.options[1].name,\n            values: newProduct.options[1].values\n          }],\n          isUpdate: productId ? 'yes' : 'no'\n        }\n      });\n      console.log(newProduct);\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n    }\n  };\n  const handleSave = async () => {\n    try {\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      let url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}`;\n      if (productId) {\n        url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}&isUpdate=yes`;\n      }\n      const res = await axios({\n        url: url,\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        },\n        data: [product]\n      });\n      console.log(res);\n      if (res.data.success) {\n        // Handle success\n      }\n    } catch (error) {\n      console.error('Error saving product:', error);\n    }\n  };\n  const handleFieldChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'short_description') {\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            [name]: '<p>' + value + '<p/>'\n          }\n        }\n      }));\n    } else if (name === 'categories') {\n      // Split the string value by comma to get an array of categories\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            categories: [value]\n          }\n        }\n      }));\n    } else if (name === 'tags') {\n      // Split the string value by comma to get an array of tags\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            tags: [value]\n          }\n        }\n      }));\n      // } else if (name === 'weight') {\n      //   setProduct((prevProduct) => ({\n      //     ...prevProduct,\n      //     newProduct: {\n      //       ...prevProduct.newProduct,\n      //       fields: {\n      //         ...prevProduct.newProduct.fields,\n      //         weight: {\n      //           ...prevProduct.newProduct.fields.weight,\n      //           value: value,\n      //         },\n      //       },\n      //     },\n      //   }));\n    } else {\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            [name]: value\n          }\n        }\n      }));\n    }\n  };\n  const handleImageChange = file => {\n    // Upload image file and update the product's photos state\n    // This function will vary depending on your backend implementation\n    // For example:\n    // const formData = new FormData();\n    // formData.append('image', file);\n    // axios.post('/upload', formData).then((response) => {\n    //    const imageUrl = response.data.imageUrl;\n    //    setProduct((prevProduct) => ({\n    //      ...prevProduct,\n    //      newProduct: {\n    //        ...prevProduct.newProduct,\n    //        photos: [...prevProduct.newProduct.photos, imageUrl],\n    //      },\n    //    }));\n    // });\n  };\n  const handleAddProduct = variants => {\n    setProduct(prevProduct => ({\n      ...prevProduct,\n      newProduct: {\n        ...prevProduct.newProduct,\n        variants: variants\n      }\n    }));\n  };\n  const handleVariantChange = (arg1, arg2, arg3) => {\n    if (Array.isArray(arg1)) {\n      // If the first argument is an array, assume it's an array of updatedVariants\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          variants: arg1\n        }\n      }));\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          options: arg2\n        }\n      }));\n    } else {\n      // If the first argument is not an array, assume individual parameters (index, property, value)\n      const index = arg1;\n      const property = arg2;\n      const value = arg3;\n      setProduct(prevProduct => {\n        const updatedVariants = [...prevProduct.newProduct.variants];\n        updatedVariants[index][property] = value;\n        return {\n          ...prevProduct,\n          newProduct: {\n            ...prevProduct.newProduct,\n            variants: updatedVariants\n          }\n        };\n      });\n    }\n  };\n  const handleBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      selectedSidebarItem: \"products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n          onClick: handleBack\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            children: \"Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 24\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 87\n          }, this),\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TitleAndDescriptionComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(MediaComponent, {\n            product: product,\n            handleImageChange: handleImageChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(InventoryComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(VariantsComponent, {\n            product: product,\n            handleVariantChange: handleVariantChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(OrganizeAndClassifyComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(WeightAndDimensionsComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddProductPage, \"SIfDUC/uHHQAsPVdsIwwQAWZZRU=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = AddProductPage;\nvar _c;\n$RefreshReg$(_c, \"AddProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "NavBar", "Sidebar", "MediaComponent", "InventoryComponent", "VariantTable", "OrganizeAndClassifyComponent", "WeightAndDimensionsComponent", "VariantsComponent", "TitleAndDescriptionComponent", "FontAwesomeIcon", "faSave", "faArrowLeft", "faUpload", "ArrowBackIcon", "<PERSON><PERSON>", "Dialog", "Grid", "Paper", "TextField", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "DialogTitle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddProductPage", "_s", "productId", "navigate", "dialogVisible", "setDialogVisible", "openDialog", "product", "setProduct", "newProduct", "fields", "title", "tags", "brand", "weight", "unit", "value", "dimensions", "width", "height", "length", "short_description", "categories", "photos", "variants", "options", "option1", "name", "values", "option2", "isUpdate", "fetchProductDetails", "response", "post", "process", "env", "REACT_APP_BASE_URL", "isCreated", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "data", "price", "quantity", "inventory_quantity", "sku", "barcode", "asin", "weight_value", "weight_unit", "dimensions_unit", "dimensions_width", "dimensions_height", "dimensions_length", "description", "category", "map", "photo", "image", "variant", "id", "item_id", "specialPrice", "skuFamily", "console", "log", "error", "handleSave", "storedGroupCode", "localStorage", "getItem", "url", "res", "method", "success", "handleFieldChange", "e", "target", "prevProduct", "handleImageChange", "file", "handleAddProduct", "handleVariantChange", "arg1", "arg2", "arg3", "Array", "isArray", "index", "property", "updatedVariants", "handleBack", "children", "selectedSidebarItem", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "color", "startIcon", "icon", "container", "spacing", "item", "xs", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Product/Addproduct/AddProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { NavBar } from '../../../components/Navbar/Navbar';\r\nimport { Sidebar } from '../../../components/SidePanel/Sidebar.jsx';\r\nimport { MediaComponent, InventoryComponent, VariantTable, OrganizeAndClassifyComponent, WeightAndDimensionsComponent, VariantsComponent, TitleAndDescriptionComponent } from './AddProductComponent.jsx';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faSave, faArrowLeft, faUpload } from '@fortawesome/free-solid-svg-icons';\r\nimport './addProductPage.css';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport { Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, DialogTitle } from '@mui/material'; // Import Material-UI components\r\n\r\n\r\nexport const AddProductPage = () => {\r\n  const { productId } = useParams();\r\n  const navigate = useNavigate();\r\n  const [dialogVisible, setDialogVisible] = useState(false); // Define dialog visibility state\r\n\r\n  // Define openDialog function\r\n  const openDialog = () => {\r\n    setDialogVisible(true);\r\n  };\r\n\r\n  const [product, setProduct] = useState({\r\n    newProduct: {\r\n      fields: {\r\n        title: '',\r\n        tags: '',\r\n        brand: '',\r\n        weight: { unit: 'kg', value: '' },\r\n        dimensions: { unit: 'm', width: '', height: '', length: '' },\r\n        short_description: '',\r\n        categories: [''],\r\n      },\r\n      photos: ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG', 'https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\r\n      variants: [],\r\n      options: {\r\n        option1: { name: 'color', values: ['red'] },\r\n        option2: { name: 'color', values: ['black'] },\r\n      },\r\n      isUpdate: 'no',\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (productId) {\r\n      fetchProductDetails(productId);\r\n    }\r\n  }, [productId]);\r\n\r\n  const fetchProductDetails = async (productId) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_BASE_URL}/common/api/get_product/?fby_user_id=8&sku=${productId}`,\r\n        {\r\n          isCreated: true,\r\n        },\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n          },\r\n        }\r\n      );\r\n      const newProduct = response.data.newProduct;\r\n      setProduct({\r\n        newProduct: {\r\n          fields: {\r\n            title: newProduct.fields.title,\r\n            tags: newProduct.fields.tags,\r\n            brand: newProduct.fields.brand,\r\n            price: newProduct.fields.price,\r\n            quantity: newProduct.fields.inventory_quantity,\r\n            sku: newProduct.fields.sku,\r\n            barcode: newProduct.fields.barcode,\r\n            asin: newProduct.fields.asin,\r\n            weight: {\r\n              value: newProduct.fields.weight_value,\r\n              unit: newProduct.fields.weight_unit\r\n            },\r\n            dimensions: {\r\n              unit: newProduct.fields.dimensions_unit || 'm',\r\n              width: newProduct.fields.dimensions_width || '',\r\n              height: newProduct.fields.dimensions_height || '',\r\n              length: newProduct.fields.dimensions_length || '',\r\n            },\r\n            short_description: newProduct.fields.description,\r\n            categories: [newProduct.fields.category],\r\n          },\r\n          photos: newProduct.photos.map(photo => photo.image),\r\n          variants: newProduct.variants.map(variant => ({\r\n            id: variant.id,\r\n            sku: variant.sku,\r\n            barcode: variant.barcode,\r\n            item_id: variant.item_id,\r\n            title: variant.title,\r\n            inventory_quantity: variant.inventory_quantity,\r\n            image: variant.image,\r\n            price: variant.price,\r\n            specialPrice: variant.specialPrice,\r\n            skuFamily: variant.skuFamily,\r\n          })),\r\n          options: [\r\n            { name: newProduct.options[0].name, values: newProduct.options[0].values },\r\n            { name: newProduct.options[1].name, values: newProduct.options[1].values }\r\n          ],\r\n          isUpdate: productId ? 'yes' : 'no',\r\n        },\r\n      });\r\n      console.log(newProduct);\r\n\r\n    } catch (error) {\r\n      console.error('Error fetching product details:', error);\r\n    }\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n      let url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}`;\r\n      if (productId) {\r\n        url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}&isUpdate=yes`;\r\n      }\r\n      const res = await axios({\r\n        url: url,\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n        },\r\n        data: [product],\r\n      });\r\n\r\n      console.log(res);\r\n      if (res.data.success) {\r\n        // Handle success\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving product:', error);\r\n    }\r\n  };\r\n\r\n  const handleFieldChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === 'short_description') {\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            [name]: '<p>' + value + '<p/>',\r\n          },\r\n        },\r\n      }));\r\n\r\n    } else if (name === 'categories') {\r\n      // Split the string value by comma to get an array of categories\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            categories: [value],\r\n          },\r\n        },\r\n      }));\r\n    } else if (name === 'tags') {\r\n      // Split the string value by comma to get an array of tags\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            tags: [value],\r\n\r\n          },\r\n        },\r\n      }));\r\n      // } else if (name === 'weight') {\r\n      //   setProduct((prevProduct) => ({\r\n      //     ...prevProduct,\r\n      //     newProduct: {\r\n      //       ...prevProduct.newProduct,\r\n      //       fields: {\r\n      //         ...prevProduct.newProduct.fields,\r\n      //         weight: {\r\n      //           ...prevProduct.newProduct.fields.weight,\r\n      //           value: value,\r\n      //         },\r\n      //       },\r\n      //     },\r\n      //   }));\r\n    }\r\n    else {\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            [name]: value,\r\n          },\r\n        },\r\n      }));\r\n    }\r\n  };\r\n\r\n\r\n  const handleImageChange = (file) => {\r\n    // Upload image file and update the product's photos state\r\n    // This function will vary depending on your backend implementation\r\n    // For example:\r\n    // const formData = new FormData();\r\n    // formData.append('image', file);\r\n    // axios.post('/upload', formData).then((response) => {\r\n    //    const imageUrl = response.data.imageUrl;\r\n    //    setProduct((prevProduct) => ({\r\n    //      ...prevProduct,\r\n    //      newProduct: {\r\n    //        ...prevProduct.newProduct,\r\n    //        photos: [...prevProduct.newProduct.photos, imageUrl],\r\n    //      },\r\n    //    }));\r\n    // });\r\n  };\r\n\r\n  const handleAddProduct = (variants) => {\r\n    setProduct((prevProduct) => ({\r\n      ...prevProduct,\r\n      newProduct: {\r\n        ...prevProduct.newProduct,\r\n        variants: variants,\r\n      },\r\n    }));\r\n  };\r\n\r\n  const handleVariantChange = (arg1, arg2, arg3) => {\r\n    if (Array.isArray(arg1)) {\r\n      // If the first argument is an array, assume it's an array of updatedVariants\r\n      setProduct(prevProduct => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          variants: arg1\r\n        },\r\n      }));\r\n      setProduct(prevProduct => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          options: arg2\r\n        },\r\n      }));\r\n    } else {\r\n      // If the first argument is not an array, assume individual parameters (index, property, value)\r\n      const index = arg1;\r\n      const property = arg2;\r\n      const value = arg3;\r\n\r\n      setProduct(prevProduct => {\r\n        const updatedVariants = [...prevProduct.newProduct.variants];\r\n        updatedVariants[index][property] = value;\r\n\r\n        return {\r\n          ...prevProduct,\r\n          newProduct: {\r\n            ...prevProduct.newProduct,\r\n            variants: updatedVariants,\r\n          },\r\n        };\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleBack = () => {\r\n    navigate(-1); // Go back to previous page\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <NavBar selectedSidebarItem=\"products\" />\r\n      <Sidebar />\r\n      <div className=\"page-content\">\r\n        <div className=\"header\">\r\n          {/* <Button onClick={handleBack} variant=\"contained\" startIcon={<FontAwesomeIcon icon={faArrowLeft} />}>\r\n            Back\r\n          </Button> */}\r\n          <ArrowBackIcon onClick={handleBack} />\r\n          <DialogTitle><b>Product</b></DialogTitle>\r\n          <Button onClick={handleSave} variant=\"contained\" color=\"primary\" startIcon={<FontAwesomeIcon icon={faSave} />}>\r\n            Save\r\n          </Button>\r\n        </div>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <TitleAndDescriptionComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <MediaComponent product={product} handleImageChange={handleImageChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            {/* Inventory component */}\r\n            <InventoryComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <VariantsComponent product={product} handleVariantChange={handleVariantChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <OrganizeAndClassifyComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <WeightAndDimensionsComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n      </div>\r\n    </>\r\n  );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,OAAO,QAAQ,2CAA2C;AACnE,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,iBAAiB,EAAEC,4BAA4B,QAAQ,2BAA2B;AACzM,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,mCAAmC;AACjF,OAAO,sBAAsB;AAC7B,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,eAAe,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGvJ,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAU,CAAC,GAAGlC,SAAS,CAAC,CAAC;EACjC,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAMyC,UAAU,GAAGA,CAAA,KAAM;IACvBD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC;IACrC4C,UAAU,EAAE;MACVC,MAAM,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;UAAEC,IAAI,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAG,CAAC;QACjCC,UAAU,EAAE;UAAEF,IAAI,EAAE,GAAG;UAAEG,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAC;QAC5DC,iBAAiB,EAAE,EAAE;QACrBC,UAAU,EAAE,CAAC,EAAE;MACjB,CAAC;MACDC,MAAM,EAAE,CAAC,gGAAgG,EAAE,gGAAgG,CAAC;MAC5MC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;QACPC,OAAO,EAAE;UAAEC,IAAI,EAAE,OAAO;UAAEC,MAAM,EAAE,CAAC,KAAK;QAAE,CAAC;QAC3CC,OAAO,EAAE;UAAEF,IAAI,EAAE,OAAO;UAAEC,MAAM,EAAE,CAAC,OAAO;QAAE;MAC9C,CAAC;MACDE,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;EAEFhE,SAAS,CAAC,MAAM;IACd,IAAIoC,SAAS,EAAE;MACb6B,mBAAmB,CAAC7B,SAAS,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAM6B,mBAAmB,GAAG,MAAO7B,SAAS,IAAK;IAC/C,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,IAAI,CAC/B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,8CAA8ClC,SAAS,EAAE,EAC1F;QACEmC,SAAS,EAAE;MACb,CAAC,EACD;QACEC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC7D;MACF,CACF,CAAC;MACD,MAAM/B,UAAU,GAAGuB,QAAQ,CAACS,IAAI,CAAChC,UAAU;MAC3CD,UAAU,CAAC;QACTC,UAAU,EAAE;UACVC,MAAM,EAAE;YACNC,KAAK,EAAEF,UAAU,CAACC,MAAM,CAACC,KAAK;YAC9BC,IAAI,EAAEH,UAAU,CAACC,MAAM,CAACE,IAAI;YAC5BC,KAAK,EAAEJ,UAAU,CAACC,MAAM,CAACG,KAAK;YAC9B6B,KAAK,EAAEjC,UAAU,CAACC,MAAM,CAACgC,KAAK;YAC9BC,QAAQ,EAAElC,UAAU,CAACC,MAAM,CAACkC,kBAAkB;YAC9CC,GAAG,EAAEpC,UAAU,CAACC,MAAM,CAACmC,GAAG;YAC1BC,OAAO,EAAErC,UAAU,CAACC,MAAM,CAACoC,OAAO;YAClCC,IAAI,EAAEtC,UAAU,CAACC,MAAM,CAACqC,IAAI;YAC5BjC,MAAM,EAAE;cACNE,KAAK,EAAEP,UAAU,CAACC,MAAM,CAACsC,YAAY;cACrCjC,IAAI,EAAEN,UAAU,CAACC,MAAM,CAACuC;YAC1B,CAAC;YACDhC,UAAU,EAAE;cACVF,IAAI,EAAEN,UAAU,CAACC,MAAM,CAACwC,eAAe,IAAI,GAAG;cAC9ChC,KAAK,EAAET,UAAU,CAACC,MAAM,CAACyC,gBAAgB,IAAI,EAAE;cAC/ChC,MAAM,EAAEV,UAAU,CAACC,MAAM,CAAC0C,iBAAiB,IAAI,EAAE;cACjDhC,MAAM,EAAEX,UAAU,CAACC,MAAM,CAAC2C,iBAAiB,IAAI;YACjD,CAAC;YACDhC,iBAAiB,EAAEZ,UAAU,CAACC,MAAM,CAAC4C,WAAW;YAChDhC,UAAU,EAAE,CAACb,UAAU,CAACC,MAAM,CAAC6C,QAAQ;UACzC,CAAC;UACDhC,MAAM,EAAEd,UAAU,CAACc,MAAM,CAACiC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;UACnDlC,QAAQ,EAAEf,UAAU,CAACe,QAAQ,CAACgC,GAAG,CAACG,OAAO,KAAK;YAC5CC,EAAE,EAAED,OAAO,CAACC,EAAE;YACdf,GAAG,EAAEc,OAAO,CAACd,GAAG;YAChBC,OAAO,EAAEa,OAAO,CAACb,OAAO;YACxBe,OAAO,EAAEF,OAAO,CAACE,OAAO;YACxBlD,KAAK,EAAEgD,OAAO,CAAChD,KAAK;YACpBiC,kBAAkB,EAAEe,OAAO,CAACf,kBAAkB;YAC9Cc,KAAK,EAAEC,OAAO,CAACD,KAAK;YACpBhB,KAAK,EAAEiB,OAAO,CAACjB,KAAK;YACpBoB,YAAY,EAAEH,OAAO,CAACG,YAAY;YAClCC,SAAS,EAAEJ,OAAO,CAACI;UACrB,CAAC,CAAC,CAAC;UACHtC,OAAO,EAAE,CACP;YAAEE,IAAI,EAAElB,UAAU,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI;YAAEC,MAAM,EAAEnB,UAAU,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACG;UAAO,CAAC,EAC1E;YAAED,IAAI,EAAElB,UAAU,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI;YAAEC,MAAM,EAAEnB,UAAU,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACG;UAAO,CAAC,CAC3E;UACDE,QAAQ,EAAE5B,SAAS,GAAG,KAAK,GAAG;QAChC;MACF,CAAC,CAAC;MACF8D,OAAO,CAACC,GAAG,CAACxD,UAAU,CAAC;IAEzB,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,IAAIC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACvD,IAAIC,GAAG,GAAG,GAAGrC,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDgC,eAAe,QAAQ7D,OAAO,CAACE,UAAU,CAACC,MAAM,CAACmC,GAAG,EAAE;MAClJ,IAAI3C,SAAS,EAAE;QACbqE,GAAG,GAAG,GAAGrC,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDgC,eAAe,QAAQ7D,OAAO,CAACE,UAAU,CAACC,MAAM,CAACmC,GAAG,eAAe;MAC7J;MACA,MAAM2B,GAAG,GAAG,MAAMzG,KAAK,CAAC;QACtBwG,GAAG,EAAEA,GAAG;QACRE,MAAM,EAAE,MAAM;QACdnC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC7D,CAAC;QACDC,IAAI,EAAE,CAAClC,OAAO;MAChB,CAAC,CAAC;MAEFyD,OAAO,CAACC,GAAG,CAACO,GAAG,CAAC;MAChB,IAAIA,GAAG,CAAC/B,IAAI,CAACiC,OAAO,EAAE;QACpB;MAAA;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMS,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEjD,IAAI;MAAEX;IAAM,CAAC,GAAG4D,CAAC,CAACC,MAAM;IAChC,IAAIlD,IAAI,KAAK,mBAAmB,EAAE;MAChCnB,UAAU,CAAEsE,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGoE,WAAW,CAACrE,UAAU,CAACC,MAAM;YAChC,CAACiB,IAAI,GAAG,KAAK,GAAGX,KAAK,GAAG;UAC1B;QACF;MACF,CAAC,CAAC,CAAC;IAEL,CAAC,MAAM,IAAIW,IAAI,KAAK,YAAY,EAAE;MAChC;MACAnB,UAAU,CAAEsE,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGoE,WAAW,CAACrE,UAAU,CAACC,MAAM;YAChCY,UAAU,EAAE,CAACN,KAAK;UACpB;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIW,IAAI,KAAK,MAAM,EAAE;MAC1B;MACAnB,UAAU,CAAEsE,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGoE,WAAW,CAACrE,UAAU,CAACC,MAAM;YAChCE,IAAI,EAAE,CAACI,KAAK;UAEd;QACF;MACF,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,MACI;MACHR,UAAU,CAAEsE,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGoE,WAAW,CAACrE,UAAU,CAACC,MAAM;YAChC,CAACiB,IAAI,GAAGX;UACV;QACF;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAGD,MAAM+D,iBAAiB,GAAIC,IAAI,IAAK;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;EAED,MAAMC,gBAAgB,GAAIzD,QAAQ,IAAK;IACrChB,UAAU,CAAEsE,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACdrE,UAAU,EAAE;QACV,GAAGqE,WAAW,CAACrE,UAAU;QACzBe,QAAQ,EAAEA;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0D,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAChD,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;MACvB;MACA3E,UAAU,CAACsE,WAAW,KAAK;QACzB,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBe,QAAQ,EAAE2D;QACZ;MACF,CAAC,CAAC,CAAC;MACH3E,UAAU,CAACsE,WAAW,KAAK;QACzB,GAAGA,WAAW;QACdrE,UAAU,EAAE;UACV,GAAGqE,WAAW,CAACrE,UAAU;UACzBgB,OAAO,EAAE2D;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA,MAAMI,KAAK,GAAGL,IAAI;MAClB,MAAMM,QAAQ,GAAGL,IAAI;MACrB,MAAMpE,KAAK,GAAGqE,IAAI;MAElB7E,UAAU,CAACsE,WAAW,IAAI;QACxB,MAAMY,eAAe,GAAG,CAAC,GAAGZ,WAAW,CAACrE,UAAU,CAACe,QAAQ,CAAC;QAC5DkE,eAAe,CAACF,KAAK,CAAC,CAACC,QAAQ,CAAC,GAAGzE,KAAK;QAExC,OAAO;UACL,GAAG8D,WAAW;UACdrE,UAAU,EAAE;YACV,GAAGqE,WAAW,CAACrE,UAAU;YACzBe,QAAQ,EAAEkE;UACZ;QACF,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBxF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACEN,OAAA,CAAAE,SAAA;IAAA6F,QAAA,gBACE/F,OAAA,CAAC3B,MAAM;MAAC2H,mBAAmB,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCpG,OAAA,CAAC1B,OAAO;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXpG,OAAA;MAAKqG,SAAS,EAAC,cAAc;MAAAN,QAAA,gBAC3B/F,OAAA;QAAKqG,SAAS,EAAC,QAAQ;QAAAN,QAAA,gBAIrB/F,OAAA,CAACd,aAAa;UAACoH,OAAO,EAAER;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCpG,OAAA,CAACF,WAAW;UAAAiG,QAAA,eAAC/F,OAAA;YAAA+F,QAAA,EAAG;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzCpG,OAAA,CAACb,MAAM;UAACmH,OAAO,EAAEhC,UAAW;UAACR,OAAO,EAAC,WAAW;UAACyC,KAAK,EAAC,SAAS;UAACC,SAAS,eAAExG,OAAA,CAAClB,eAAe;YAAC2H,IAAI,EAAE1H;UAAO;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAAC;QAE/G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChB/F,OAAA,CAACnB,4BAA4B;YAAC6B,OAAO,EAAEA,OAAQ;YAACoE,iBAAiB,EAAEA;UAAkB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChB/F,OAAA,CAACzB,cAAc;YAACmC,OAAO,EAAEA,OAAQ;YAACwE,iBAAiB,EAAEA;UAAkB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAEhB/F,OAAA,CAACxB,kBAAkB;YAACkC,OAAO,EAAEA,OAAQ;YAACoE,iBAAiB,EAAEA;UAAkB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChB/F,OAAA,CAACpB,iBAAiB;YAAC8B,OAAO,EAAEA,OAAQ;YAAC2E,mBAAmB,EAAEA;UAAoB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChB/F,OAAA,CAACtB,4BAA4B;YAACgC,OAAO,EAAEA,OAAQ;YAACoE,iBAAiB,EAAEA;UAAkB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpG,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzB/F,OAAA,CAACX,IAAI;UAACuH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChB/F,OAAA,CAACrB,4BAA4B;YAAC+B,OAAO,EAAEA,OAAQ;YAACoE,iBAAiB,EAAEA;UAAkB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAChG,EAAA,CA7TWD,cAAc;EAAA,QACHhC,SAAS,EACdC,WAAW;AAAA;AAAA0I,EAAA,GAFjB3G,cAAc;AAAA,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}