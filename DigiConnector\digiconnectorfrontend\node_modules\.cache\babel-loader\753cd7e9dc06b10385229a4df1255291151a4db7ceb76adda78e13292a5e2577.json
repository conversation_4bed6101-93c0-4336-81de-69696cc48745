{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Channels\\\\Channel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { Grid, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';\nimport { Sidebar } from '../../components/SidePanel/Sidebar';\nimport { NavBar } from '../../components/Navbar/Navbar';\nimport ShopifyLogo from '../../Images/shopify.png';\nimport AmazonLogo from '../../Images/amazon.png';\nimport WooCommerceLogo from '../../Images/woocommerce.png';\nimport StordenLogo from '../../Images/amazon.png';\nimport PrestaShopLogo from '../../Images/prestashop.png';\nimport eBayLogo from '../../Images/ebay.png';\nimport Magento<PERSON>ogo from '../../Images/magento.png';\nimport EtsyLogo from '../../Images/etsy.png';\nimport MiraklLogo from '../../Images/mirakl.png';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport './channelPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst channelDetails = {\n  shopify: ShopifyLogo,\n  amazon: AmazonLogo,\n  woocommerce: WooCommerceLogo,\n  storden: StordenLogo,\n  prestashop: PrestaShopLogo,\n  ebay: eBayLogo,\n  magento: MagentoLogo,\n  etsy: EtsyLogo,\n  mirakl: MiraklLogo\n};\nexport const Channel = () => {\n  _s();\n  const navigate = useNavigate();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [formData, setFormData] = useState({});\n  const [currentChannel, setCurrentChannel] = useState('');\n  const handleConnectClick = channelName => {\n    // Set current channel and open dialog\n    setCurrentChannel(channelName);\n    setDialogOpen(true);\n  };\n  const handleClose = () => {\n    // Reset form data and close dialog\n    setFormData({});\n    setDialogOpen(false);\n  };\n  const handleBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n  const handleConnect = async () => {\n    try {\n      const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/channel`, {\n        action: 'connect',\n        channel: currentChannel,\n        formData\n      }, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        }\n      });\n      console.log('API response:', response.data);\n\n      // Handle response data as needed\n      handleClose(); // Close dialog after connecting\n    } catch (error) {\n      console.error('Error connecting to channel:', error);\n    }\n  };\n  const handleChange = e => {\n    // Update form data\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const getDialogContent = () => {\n    switch (currentChannel) {\n      case 'shopify':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"URL of Store\",\n              name: \"storeUrl\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Token\",\n              name: \"token\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 21\n        }, this);\n      case 'amazon':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store Name\",\n              name: \"storeName\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"AWS Access key ID\",\n              name: \"accessKeyId\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Client Secret\",\n              name: \"clientSecret\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Market Place Id\",\n              name: \"marketPlaceId\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"AWS Authorization Token\",\n              name: \"awsAuthToken\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Seller/Merchant Id\",\n              name: \"merchantId\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this);\n      case 'woocommerce':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store Url\",\n              name: \"storeUrl\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Consumer Key\",\n              name: \"consumerKey\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Consumer Secret\",\n              name: \"consumerSecret\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this);\n      case 'storden':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store Name\",\n              name: \"storeName\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Access Token\",\n              name: \"accessToken\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this);\n      case 'prestashop':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store URL\",\n              name: \"storeUrl\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"API Key\",\n              name: \"apiKey\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this);\n      case 'ebay':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store Name\",\n              name: \"storeName\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Access Token\",\n              name: \"accessToken\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this);\n      case 'magento':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store URL\",\n              name: \"storeUrl\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Access Token\",\n              name: \"accessToken\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this);\n      case 'etsy':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Store Name\",\n              name: \"storeName\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"API Key\",\n              name: \"apiKey\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this);\n      case 'mirakl':\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Channel Number\",\n              name: \"fby_user_id\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Mirakl API URL\",\n              name: \"apiUrl\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"API Key\",\n              name: \"apiKey\",\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 21\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const channels = ['shopify', 'amazon', 'woocommerce', 'storden', 'prestashop', 'ebay', 'magento', 'etsy', 'mirakl'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"channel-page\",\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      selectedSidebarItem: \"channel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"channel-content\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 57\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Channels\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: channels.map(channelName => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 4,\n          className: \"channel-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: channelDetails[channelName],\n            alt: channelName,\n            className: \"channel-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-container\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: () => handleConnectClick(channelName),\n              children: \"Connect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 29\n          }, this)]\n        }, channelName, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleClose,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          fontSize: '26px'\n        },\n        children: [\"Connect to \", currentChannel]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: getDialogContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConnect,\n          variant: \"contained\",\n          color: \"primary\",\n          children: \"Connect\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 9\n  }, this);\n};\n_s(Channel, \"AYKC0vzagc9Kwk/12yWX5FALy/s=\", false, function () {\n  return [useNavigate];\n});\n_c = Channel;\nvar _c;\n$RefreshReg$(_c, \"Channel\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "axios", "Grid", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Sidebar", "NavBar", "ShopifyLogo", "AmazonLogo", "WooCommerceLogo", "StordenLogo", "PrestaShopLogo", "eBayLogo", "MagentoLogo", "EtsyLogo", "MiraklLogo", "ArrowBackIcon", "jsxDEV", "_jsxDEV", "channelDetails", "shopify", "amazon", "woocommerce", "storden", "prestashop", "ebay", "magento", "etsy", "mirakl", "Channel", "_s", "navigate", "dialogOpen", "setDialogOpen", "formData", "setFormData", "currentChannel", "setCurrentChannel", "handleConnectClick", "channelName", "handleClose", "handleBack", "handleConnect", "response", "post", "process", "env", "REACT_APP_BASE_URL", "action", "channel", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "console", "log", "data", "error", "handleChange", "e", "target", "name", "value", "getD<PERSON>og<PERSON>ontent", "container", "spacing", "children", "item", "xs", "fullWidth", "label", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "channels", "className", "selectedSidebarItem", "onClick", "startIcon", "map", "sm", "md", "lg", "src", "alt", "variant", "color", "open", "onClose", "sx", "fontSize", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Channels/Channel.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { Grid, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';\r\nimport { Sidebar } from '../../components/SidePanel/Sidebar';\r\nimport { NavBar } from '../../components/Navbar/Navbar';\r\nimport ShopifyLogo from '../../Images/shopify.png';\r\nimport AmazonLogo from '../../Images/amazon.png';\r\nimport WooCommerceLogo from '../../Images/woocommerce.png';\r\nimport StordenLogo from '../../Images/amazon.png';\r\nimport PrestaShopLogo from '../../Images/prestashop.png';\r\nimport eBayLogo from '../../Images/ebay.png';\r\nimport MagentoLogo from '../../Images/magento.png';\r\nimport EtsyLogo from '../../Images/etsy.png';\r\nimport MiraklLogo from '../../Images/mirakl.png';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport './channelPage.css';\r\n\r\nconst channelDetails = {\r\n    shopify: ShopifyLogo,\r\n    amazon: AmazonLogo,\r\n    woocommerce: WooCommerceLogo,\r\n    storden: StordenLogo,\r\n    prestashop: PrestaShopLogo,\r\n    ebay: eBayLogo,\r\n    magento: MagentoLogo,\r\n    etsy: EtsyLogo,\r\n    mirakl: MiraklLogo\r\n};\r\n\r\nexport const Channel = () => {\r\n    const navigate = useNavigate();\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const [formData, setFormData] = useState({});\r\n    const [currentChannel, setCurrentChannel] = useState('');\r\n\r\n    const handleConnectClick = (channelName) => {\r\n        // Set current channel and open dialog\r\n        setCurrentChannel(channelName);\r\n        setDialogOpen(true);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        // Reset form data and close dialog\r\n        setFormData({});\r\n        setDialogOpen(false);\r\n    };\r\n\r\n    const handleBack = () => {\r\n        navigate(-1); // Go back to previous page\r\n    };\r\n\r\n    const handleConnect = async () => {\r\n        try {\r\n            const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/channel`, {\r\n                action: 'connect',\r\n                channel: currentChannel,\r\n                formData\r\n            },{\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n                }\r\n            });\r\n\r\n            console.log('API response:', response.data);\r\n\r\n            // Handle response data as needed\r\n            handleClose(); // Close dialog after connecting\r\n        } catch (error) {\r\n            console.error('Error connecting to channel:', error);\r\n        }\r\n    };\r\n\r\n    const handleChange = (e) => {\r\n        // Update form data\r\n        setFormData({\r\n            ...formData,\r\n            [e.target.name]: e.target.value\r\n        });\r\n    };\r\n\r\n    const getDialogContent = () => {\r\n        switch (currentChannel) {\r\n            case 'shopify':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"URL of Store\" name=\"storeUrl\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Token\" name=\"token\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'amazon':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store Name\" name=\"storeName\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"AWS Access key ID\" name=\"accessKeyId\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Client Secret\" name=\"clientSecret\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Market Place Id\" name=\"marketPlaceId\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"AWS Authorization Token\" name=\"awsAuthToken\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Seller/Merchant Id\" name=\"merchantId\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'woocommerce':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store Url\" name=\"storeUrl\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Consumer Key\" name=\"consumerKey\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Consumer Secret\" name=\"consumerSecret\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'storden':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store Name\" name=\"storeName\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Access Token\" name=\"accessToken\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'prestashop':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store URL\" name=\"storeUrl\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"API Key\" name=\"apiKey\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'ebay':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store Name\" name=\"storeName\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Access Token\" name=\"accessToken\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'magento':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store URL\" name=\"storeUrl\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Access Token\" name=\"accessToken\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'etsy':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Store Name\" name=\"storeName\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"API Key\" name=\"apiKey\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            case 'mirakl':\r\n                return (\r\n                    <Grid container spacing={2}>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Channel Number\" name=\"fby_user_id\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"Mirakl API URL\" name=\"apiUrl\" onChange={handleChange} />\r\n                        </Grid>\r\n                        <Grid item xs={12}>\r\n                            <TextField fullWidth label=\"API Key\" name=\"apiKey\" onChange={handleChange} />\r\n                        </Grid>\r\n                    </Grid>\r\n                );\r\n            default:\r\n                return null;\r\n        }\r\n    };\r\n\r\n    const channels = ['shopify', 'amazon', 'woocommerce', 'storden', 'prestashop', 'ebay', 'magento', 'etsy', 'mirakl'];\r\n\r\n    return (\r\n        <div className=\"channel-page\">\r\n            <NavBar selectedSidebarItem=\"channel\" />\r\n            <Sidebar />\r\n            <div className=\"channel-content\">\r\n                <Button onClick={handleBack} startIcon={<ArrowBackIcon />}>\r\n                    Back\r\n                </Button>\r\n                <h2>Channels</h2>\r\n                <Grid container spacing={2}>\r\n                    {channels.map((channelName) => (\r\n                        <Grid item xs={12} sm={6} md={4} lg={4} key={channelName} className=\"channel-item\">\r\n                            <img src={channelDetails[channelName]} alt={channelName} className=\"channel-image\" />\r\n                            <div className=\"button-container\">\r\n                                <Button variant=\"contained\" color=\"primary\" onClick={() => handleConnectClick(channelName)}>Connect</Button>\r\n                            </div>\r\n                        </Grid>\r\n                    ))}\r\n                </Grid>\r\n            </div>\r\n            <Dialog open={dialogOpen} onClose={handleClose}>\r\n                <DialogTitle sx={{ fontSize: '26px' }}>Connect to {currentChannel}</DialogTitle>\r\n                <DialogContent>\r\n                    {getDialogContent()}\r\n                </DialogContent>\r\n                <DialogActions>\r\n                    <Button onClick={handleClose}>Cancel</Button>\r\n                    <Button onClick={handleConnect} variant=\"contained\" color=\"primary\">Connect</Button>\r\n                </DialogActions>\r\n            </Dialog>\r\n        </div>\r\n    );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,SAAS,QAAQ,eAAe;AAC1G,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,gCAAgC;AACvD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,cAAc,GAAG;EACnBC,OAAO,EAAEb,WAAW;EACpBc,MAAM,EAAEb,UAAU;EAClBc,WAAW,EAAEb,eAAe;EAC5Bc,OAAO,EAAEb,WAAW;EACpBc,UAAU,EAAEb,cAAc;EAC1Bc,IAAI,EAAEb,QAAQ;EACdc,OAAO,EAAEb,WAAW;EACpBc,IAAI,EAAEb,QAAQ;EACdc,MAAM,EAAEb;AACZ,CAAC;AAED,OAAO,MAAMc,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM2C,kBAAkB,GAAIC,WAAW,IAAK;IACxC;IACAF,iBAAiB,CAACE,WAAW,CAAC;IAC9BN,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACtB;IACAL,WAAW,CAAC,CAAC,CAAC,CAAC;IACfF,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACrBV,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMW,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,UAAU,EAAE;QAC3EC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAEb,cAAc;QACvBF;MACJ,CAAC,EAAC;QACEgB,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUN,OAAO,CAACC,GAAG,CAACM,sBAAsB;QAC/D;MACJ,CAAC,CAAC;MAEFC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEX,QAAQ,CAACY,IAAI,CAAC;;MAE3C;MACAf,WAAW,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACZH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IACxB;IACAvB,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACwB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQ1B,cAAc;MAClB,KAAK,SAAS;QACV,oBACIlB,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,cAAc;cAACT,IAAI,EAAC,UAAU;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,OAAO;cAACT,IAAI,EAAC,OAAO;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,QAAQ;QACT,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,YAAY;cAACT,IAAI,EAAC,WAAW;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,mBAAmB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,eAAe;cAACT,IAAI,EAAC,cAAc;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,iBAAiB;cAACT,IAAI,EAAC,eAAe;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,yBAAyB;cAACT,IAAI,EAAC,cAAc;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,oBAAoB;cAACT,IAAI,EAAC,YAAY;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,aAAa;QACd,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,WAAW;cAACT,IAAI,EAAC,UAAU;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,cAAc;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,iBAAiB;cAACT,IAAI,EAAC,gBAAgB;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,SAAS;QACV,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,YAAY;cAACT,IAAI,EAAC,WAAW;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,cAAc;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,YAAY;QACb,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,WAAW;cAACT,IAAI,EAAC,UAAU;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,SAAS;cAACT,IAAI,EAAC,QAAQ;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,MAAM;QACP,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,YAAY;cAACT,IAAI,EAAC,WAAW;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,cAAc;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,SAAS;QACV,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,WAAW;cAACT,IAAI,EAAC,UAAU;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,cAAc;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,MAAM;QACP,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,YAAY;cAACT,IAAI,EAAC,WAAW;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,SAAS;cAACT,IAAI,EAAC,QAAQ;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf,KAAK,QAAQ;QACT,oBACIxD,OAAA,CAACpB,IAAI;UAACiE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAC,QAAA,gBACvB/C,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,aAAa;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAACT,IAAI,EAAC,QAAQ;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACPxD,OAAA,CAACpB,IAAI;YAACoE,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eACd/C,OAAA,CAACd,SAAS;cAACgE,SAAS;cAACC,KAAK,EAAC,SAAS;cAACT,IAAI,EAAC,QAAQ;cAACU,QAAQ,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEf;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;EAEnH,oBACIzD,OAAA;IAAK0D,SAAS,EAAC,cAAc;IAAAX,QAAA,gBACzB/C,OAAA,CAACZ,MAAM;MAACuE,mBAAmB,EAAC;IAAS;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxCxD,OAAA,CAACb,OAAO;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXxD,OAAA;MAAK0D,SAAS,EAAC,iBAAiB;MAAAX,QAAA,gBAC5B/C,OAAA,CAACnB,MAAM;QAAC+E,OAAO,EAAErC,UAAW;QAACsC,SAAS,eAAE7D,OAAA,CAACF,aAAa;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAT,QAAA,EAAC;MAE3D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QAAA+C,QAAA,EAAI;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBxD,OAAA,CAACpB,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAC,QAAA,EACtBU,QAAQ,CAACK,GAAG,CAAEzC,WAAW,iBACtBrB,OAAA,CAACpB,IAAI;UAACoE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAmBP,SAAS,EAAC,cAAc;UAAAX,QAAA,gBAC9E/C,OAAA;YAAKkE,GAAG,EAAEjE,cAAc,CAACoB,WAAW,CAAE;YAAC8C,GAAG,EAAE9C,WAAY;YAACqC,SAAS,EAAC;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFxD,OAAA;YAAK0D,SAAS,EAAC,kBAAkB;YAAAX,QAAA,eAC7B/C,OAAA,CAACnB,MAAM;cAACuF,OAAO,EAAC,WAAW;cAACC,KAAK,EAAC,SAAS;cAACT,OAAO,EAAEA,CAAA,KAAMxC,kBAAkB,CAACC,WAAW,CAAE;cAAA0B,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC;QAAA,GAJmCnC,WAAW;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKlD,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNxD,OAAA,CAAClB,MAAM;MAACwF,IAAI,EAAExD,UAAW;MAACyD,OAAO,EAAEjD,WAAY;MAAAyB,QAAA,gBAC3C/C,OAAA,CAACjB,WAAW;QAACyF,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAA1B,QAAA,GAAC,aAAW,EAAC7B,cAAc;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAChFxD,OAAA,CAAChB,aAAa;QAAA+D,QAAA,EACTH,gBAAgB,CAAC;MAAC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAChBxD,OAAA,CAACf,aAAa;QAAA8D,QAAA,gBACV/C,OAAA,CAACnB,MAAM;UAAC+E,OAAO,EAAEtC,WAAY;UAAAyB,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CxD,OAAA,CAACnB,MAAM;UAAC+E,OAAO,EAAEpC,aAAc;UAAC4C,OAAO,EAAC,WAAW;UAACC,KAAK,EAAC,SAAS;UAAAtB,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAC5C,EAAA,CA1OWD,OAAO;EAAA,QACCjC,WAAW;AAAA;AAAAgG,EAAA,GADnB/D,OAAO;AAAA,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}